#!/usr/bin/env python3
"""
Utility functions for working with Braintrust dataset timestamps.

This module provides helper functions to:
- Extract and parse created timestamps from dataset records
- Track record updates using transaction IDs
- Add custom update tracking to dataset records
- Query records by date ranges
"""

import braintrust
from datetime import datetime, timezone
from typing import Dict, Any, List, Optional, Tuple, Iterator
import json


class DatasetTimestampTracker:
    """Helper class for tracking timestamps and updates in Braintrust datasets."""
    
    def __init__(self, project: str, dataset_name: str):
        """Initialize the tracker with a specific dataset."""
        self.project = project
        self.dataset_name = dataset_name
        self.dataset = braintrust.init_dataset(project=project, name=dataset_name)
    
    @staticmethod
    def parse_created_timestamp(record: Dict[str, Any]) -> Optional[datetime]:
        """
        Parse the 'created' timestamp from a dataset record.
        
        Args:
            record: Dataset record dictionary
            
        Returns:
            datetime object or None if timestamp is not available
        """
        created_str = record.get('created')
        if not created_str:
            return None
        
        # Handle ISO format with 'Z' suffix
        if created_str.endswith('Z'):
            created_str = created_str[:-1] + '+00:00'
        
        try:
            return datetime.fromisoformat(created_str)
        except ValueError:
            return None
    
    @staticmethod
    def get_transaction_id(record: Dict[str, Any]) -> Optional[str]:
        """
        Get the transaction ID from a dataset record.
        
        Args:
            record: Dataset record dictionary
            
        Returns:
            Transaction ID string or None
        """
        return record.get('_xact_id')
    
    def add_record_with_timestamp_tracking(
        self, 
        input_data: Any, 
        expected: Any = None, 
        metadata: Optional[Dict[str, Any]] = None,
        record_id: Optional[str] = None
    ) -> str:
        """
        Add a record with custom timestamp tracking in metadata.
        
        Args:
            input_data: Input data for the record
            expected: Expected output
            metadata: Additional metadata (will be extended with timestamps)
            record_id: Optional custom record ID
            
        Returns:
            Record ID of the inserted record
        """
        if metadata is None:
            metadata = {}
        
        # Add custom timestamp tracking
        now = datetime.now(timezone.utc).isoformat()
        metadata.update({
            'custom_created_at': now,
            'custom_updated_at': now,
            'update_count': 0
        })
        
        return self.dataset.insert(
            input=input_data,
            expected=expected,
            metadata=metadata,
            id=record_id
        )
    
    def update_record_with_timestamp_tracking(
        self,
        record_id: str,
        input_data: Any = None,
        expected: Any = None,
        metadata: Optional[Dict[str, Any]] = None
    ) -> str:
        """
        Update a record and add custom update timestamp tracking.
        
        Args:
            record_id: ID of the record to update
            input_data: New input data (optional)
            expected: New expected output (optional)
            metadata: New metadata (optional, will be merged with timestamp info)
            
        Returns:
            Record ID of the updated record
        """
        # Get current record to preserve existing metadata
        current_record = self.get_record_by_id(record_id)
        if not current_record:
            raise ValueError(f"Record with ID {record_id} not found")
        
        # Prepare update metadata
        update_metadata = current_record.get('metadata', {}).copy()
        if metadata:
            update_metadata.update(metadata)
        
        # Update timestamp tracking
        now = datetime.now(timezone.utc).isoformat()
        update_count = update_metadata.get('update_count', 0) + 1
        
        update_metadata.update({
            'custom_updated_at': now,
            'update_count': update_count,
            'last_update_timestamp': now
        })
        
        return self.dataset.update(
            id=record_id,
            input=input_data,
            expected=expected,
            metadata=update_metadata
        )
    
    def get_record_by_id(self, record_id: str) -> Optional[Dict[str, Any]]:
        """
        Get a specific record by its ID.
        
        Args:
            record_id: ID of the record to retrieve
            
        Returns:
            Record dictionary or None if not found
        """
        for record in self.dataset:
            if record.get('id') == record_id:
                return record
        return None
    
    def get_records_by_date_range(
        self, 
        start_date: datetime, 
        end_date: datetime
    ) -> List[Dict[str, Any]]:
        """
        Get records created within a specific date range.
        
        Args:
            start_date: Start of date range (inclusive)
            end_date: End of date range (inclusive)
            
        Returns:
            List of records created within the date range
        """
        matching_records = []
        
        for record in self.dataset:
            created_dt = self.parse_created_timestamp(record)
            if created_dt and start_date <= created_dt <= end_date:
                matching_records.append(record)
        
        return matching_records
    
    def get_recently_updated_records(self, hours: int = 24) -> List[Dict[str, Any]]:
        """
        Get records that have been updated recently based on custom tracking.
        
        Args:
            hours: Number of hours to look back
            
        Returns:
            List of recently updated records
        """
        cutoff_time = datetime.now(timezone.utc).timestamp() - (hours * 3600)
        recently_updated = []
        
        for record in self.dataset:
            metadata = record.get('metadata', {})
            updated_at_str = metadata.get('custom_updated_at')
            
            if updated_at_str:
                try:
                    updated_dt = datetime.fromisoformat(updated_at_str.replace('Z', '+00:00'))
                    if updated_dt.timestamp() > cutoff_time:
                        recently_updated.append(record)
                except ValueError:
                    continue
        
        return recently_updated
    
    def analyze_record_timestamps(self) -> Dict[str, Any]:
        """
        Analyze timestamp patterns in the dataset.
        
        Returns:
            Dictionary with timestamp analysis results
        """
        records = list(self.dataset)
        
        if not records:
            return {"error": "No records found in dataset"}
        
        created_timestamps = []
        transaction_ids = set()
        records_with_custom_tracking = 0
        update_counts = []
        
        for record in records:
            # Analyze created timestamps
            created_dt = self.parse_created_timestamp(record)
            if created_dt:
                created_timestamps.append(created_dt)
            
            # Collect transaction IDs
            xact_id = self.get_transaction_id(record)
            if xact_id:
                transaction_ids.add(xact_id)
            
            # Check for custom timestamp tracking
            metadata = record.get('metadata', {})
            if 'custom_created_at' in metadata:
                records_with_custom_tracking += 1
                update_count = metadata.get('update_count', 0)
                update_counts.append(update_count)
        
        analysis = {
            'total_records': len(records),
            'unique_transaction_ids': len(transaction_ids),
            'records_with_custom_tracking': records_with_custom_tracking,
        }
        
        if created_timestamps:
            analysis.update({
                'earliest_created': min(created_timestamps).isoformat(),
                'latest_created': max(created_timestamps).isoformat(),
                'creation_time_span_hours': (max(created_timestamps) - min(created_timestamps)).total_seconds() / 3600
            })
        
        if update_counts:
            analysis.update({
                'total_updates': sum(update_counts),
                'avg_updates_per_record': sum(update_counts) / len(update_counts),
                'max_updates_single_record': max(update_counts)
            })
        
        return analysis
    
    def print_record_summary(self, record: Dict[str, Any]) -> None:
        """
        Print a formatted summary of a record's timestamp information.
        
        Args:
            record: Dataset record to summarize
        """
        record_id = record.get('id', 'Unknown')
        created_dt = self.parse_created_timestamp(record)
        xact_id = self.get_transaction_id(record)
        metadata = record.get('metadata', {})
        
        print(f"Record ID: {record_id}")
        
        if created_dt:
            print(f"  Created: {created_dt.strftime('%Y-%m-%d %H:%M:%S UTC')}")
        else:
            print("  Created: Not available")
        
        print(f"  Transaction ID: {xact_id}")
        
        # Custom timestamp tracking info
        if 'custom_created_at' in metadata:
            print(f"  Custom Created: {metadata['custom_created_at']}")
            print(f"  Custom Updated: {metadata.get('custom_updated_at', 'N/A')}")
            print(f"  Update Count: {metadata.get('update_count', 0)}")
        
        print(f"  Input: {record.get('input', 'N/A')}")
        print(f"  Expected: {record.get('expected', 'N/A')}")
        print("-" * 50)


# Example usage functions
def example_basic_usage():
    """Example of basic timestamp tracking usage."""
    tracker = DatasetTimestampTracker("Timestamp Demo", "basic_example")
    
    # Add records with timestamp tracking
    id1 = tracker.add_record_with_timestamp_tracking(
        input_data="Hello world",
        expected="Hello response",
        metadata={"category": "greeting"}
    )
    
    id2 = tracker.add_record_with_timestamp_tracking(
        input_data="How are you?",
        expected="I'm doing well",
        metadata={"category": "question"}
    )
    
    tracker.dataset.flush()
    
    # Update a record
    tracker.update_record_with_timestamp_tracking(
        record_id=id1,
        expected="Updated hello response",
        metadata={"category": "greeting", "version": 2}
    )
    
    tracker.dataset.flush()
    
    # Analyze the dataset
    analysis = tracker.analyze_record_timestamps()
    print("Dataset Analysis:")
    print(json.dumps(analysis, indent=2))


if __name__ == "__main__":
    example_basic_usage()
