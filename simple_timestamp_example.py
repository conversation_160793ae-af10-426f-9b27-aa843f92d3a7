#!/usr/bin/env python3
"""
Simple example showing how to capture created and updated dates of Braintrust dataset rows.

This script demonstrates the most common patterns for working with timestamps in datasets.
"""

import braintrust
from datetime import datetime
import time


def main():
    """Simple example of capturing dataset row timestamps."""
    
    # Initialize a dataset
    dataset = braintrust.init_dataset(project="pedro-project1", name="my_dataset")
    
    print("=== Creating Dataset Records ===")
    
    # Insert some records
    record_ids = []
    for i in range(3):
        record_id = dataset.insert(
            input=f"Question {i+1}",
            expected=f"Answer {i+1}",
            metadata={
                "category": "example",
                "sequence": i+1
            }
        )
        record_ids.append(record_id)
        print(f"Created record {record_id}")
    
    # Flush to save records
    dataset.flush()
    
    print("\n=== Reading Records with Timestamps ===")
    
    # Read records and show their created timestamps
    original_records = {}
    for record in dataset:
        record_id = record['id']
        created_timestamp = record['created']  # This is the creation timestamp
        transaction_id = record['_xact_id']    # This tracks updates
        
        # Parse the ISO timestamp
        if created_timestamp.endswith('Z'):
            created_timestamp = created_timestamp[:-1] + '+00:00'
        created_dt = datetime.fromisoformat(created_timestamp)
        
        print(f"Record {record_id}:")
        print(f"  Created: {created_dt.strftime('%Y-%m-%d %H:%M:%S UTC')}")
        print(f"  Transaction ID: {transaction_id}")
        print(f"  Input: {record['input']}")
        print(f"  Expected: {record['expected']}")
        print()
        
        # Store for comparison later
        original_records[record_id] = {
            'created': created_timestamp,
            'xact_id': transaction_id
        }
    
    # Wait a moment, then update some records
    print("=== Updating Records ===")
    time.sleep(1)
    
    # Update the first record
    first_record_id = record_ids[0]
    dataset.update(
        id=first_record_id,
        expected="Updated Answer 1",
        metadata={
            "category": "example", 
            "sequence": 1,
            "updated": True,
            "update_timestamp": datetime.now().isoformat()  # Custom update tracking
        }
    )
    dataset.flush()
    
    print(f"Updated record {first_record_id}")
    
    print("\n=== Checking for Updates ===")
    
    # Read records again to see what changed
    for record in dataset:
        record_id = record['id']
        current_created = record['created']
        current_xact_id = record['_xact_id']
        
        original = original_records.get(record_id, {})
        original_created = original.get('created')
        original_xact_id = original.get('xact_id')
        
        print(f"Record {record_id}:")
        
        # Check if created timestamp changed (it shouldn't)
        if current_created == original_created:
            print(f"  ✓ Created timestamp unchanged: {current_created}")
        else:
            print(f"  ⚠ Created timestamp changed: {original_created} -> {current_created}")
        
        # Check if transaction ID changed (indicates update)
        if current_xact_id != original_xact_id:
            print(f"  📝 Record was updated (xact_id: {original_xact_id} -> {current_xact_id})")
            
            # Show custom update timestamp if available
            metadata = record.get('metadata', {})
            if 'update_timestamp' in metadata:
                print(f"  📅 Custom update timestamp: {metadata['update_timestamp']}")
        else:
            print(f"  ➖ No updates (xact_id: {current_xact_id})")
        
        print(f"  Current data: {record['input']} -> {record['expected']}")
        print()
    
    print("=== Summary ===")
    print("Key points about Braintrust dataset timestamps:")
    print("• Each record has a 'created' field with ISO timestamp")
    print("• The 'created' timestamp never changes, even after updates")
    print("• Use '_xact_id' to detect when records have been updated")
    print("• Transaction IDs increase with each update operation")
    print("• Add custom timestamp fields in metadata for additional tracking")


if __name__ == "__main__":
    main()
