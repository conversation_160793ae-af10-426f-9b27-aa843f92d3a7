#!/usr/bin/env python3
"""
<PERSON>rip<PERSON> to demonstrate capturing created and updated dates of rows in a Braintrust dataset.

This script shows how to:
1. Access the 'created' timestamp field from dataset records
2. Track updates using transaction IDs (_xact_id)
3. Parse and work with ISO timestamp strings
4. Monitor changes to dataset records over time
"""

import braintrust
from datetime import datetime
from typing import Dict, Any, List
import time


def parse_iso_timestamp(iso_string: str) -> datetime:
    """Parse ISO timestamp string to datetime object."""
    # Remove 'Z' suffix if present and parse
    if iso_string.endswith('Z'):
        iso_string = iso_string[:-1] + '+00:00'
    return datetime.fromisoformat(iso_string)


def format_timestamp(dt: datetime) -> str:
    """Format datetime object for display."""
    return dt.strftime("%Y-%m-%d %H:%M:%S UTC")


def display_record_info(record: Dict[str, Any]) -> None:
    """Display timestamp information for a dataset record."""
    record_id = record.get('id', 'Unknown')
    created_str = record.get('created', '')
    xact_id = record.get('_xact_id', 'Unknown')
    
    if created_str:
        created_dt = parse_iso_timestamp(created_str)
        formatted_created = format_timestamp(created_dt)
    else:
        formatted_created = 'Not available'
    
    print(f"Record ID: {record_id}")
    print(f"  Created: {formatted_created}")
    print(f"  Transaction ID: {xact_id}")
    print(f"  Input: {record.get('input', 'N/A')}")
    print(f"  Expected: {record.get('expected', 'N/A')}")
    print(f"  Metadata: {record.get('metadata', {})}")
    print("-" * 50)


def track_dataset_changes(project_name: str, dataset_name: str) -> None:
    """
    Demonstrate tracking dataset record timestamps and changes.
    """
    print(f"Working with dataset: {project_name}/{dataset_name}")
    print("=" * 60)
    
    # Initialize dataset
    dataset = braintrust.init_dataset(project=project_name, name=dataset_name)
    
    # Insert some initial records
    print("1. Inserting initial records...")
    record_ids = []
    for i in range(3):
        record_id = dataset.insert(
            input=f"input_{i}",
            expected=f"expected_{i}",
            metadata={"version": 1, "batch": "initial"}
        )
        record_ids.append(record_id)
        print(f"   Inserted record with ID: {record_id}")
    
    # Flush to ensure records are saved
    dataset.flush()
    print("   Records flushed to server")
    
    # Wait a moment to ensure timestamps are different
    time.sleep(1)
    
    # Fetch and display initial records with timestamps
    print("\n2. Initial records with timestamps:")
    initial_records = {}
    for record in dataset:
        display_record_info(record)
        initial_records[record['id']] = {
            'created': record.get('created'),
            'xact_id': record.get('_xact_id'),
            'data': record
        }
    
    # Update some records
    print("\n3. Updating records...")
    for i, record_id in enumerate(record_ids[:2]):  # Update first 2 records
        dataset.update(
            id=record_id,
            expected=f"updated_expected_{i}",
            metadata={"version": 2, "batch": "updated", "updated_at": datetime.now().isoformat()}
        )
        print(f"   Updated record: {record_id}")
    
    # Flush updates
    dataset.flush()
    print("   Updates flushed to server")
    
    # Fetch records again to see changes
    print("\n4. Records after updates:")
    updated_records = {}
    for record in dataset:
        display_record_info(record)
        updated_records[record['id']] = {
            'created': record.get('created'),
            'xact_id': record.get('_xact_id'),
            'data': record
        }
    
    # Compare transaction IDs to detect updates
    print("\n5. Change analysis:")
    for record_id in record_ids:
        initial_xact = initial_records.get(record_id, {}).get('xact_id')
        updated_xact = updated_records.get(record_id, {}).get('xact_id')
        
        if initial_xact != updated_xact:
            print(f"   Record {record_id}: UPDATED (xact_id: {initial_xact} -> {updated_xact})")
        else:
            print(f"   Record {record_id}: No changes (xact_id: {initial_xact})")
    
    # Show created timestamps remain unchanged
    print("\n6. Created timestamp consistency check:")
    for record_id in record_ids:
        initial_created = initial_records.get(record_id, {}).get('created')
        updated_created = updated_records.get(record_id, {}).get('created')
        
        if initial_created == updated_created:
            print(f"   Record {record_id}: Created timestamp unchanged ✓")
        else:
            print(f"   Record {record_id}: Created timestamp changed (unexpected)")


def analyze_dataset_timestamps(project_name: str, dataset_name: str) -> None:
    """
    Analyze timestamps in an existing dataset.
    """
    print(f"\nAnalyzing timestamps in dataset: {project_name}/{dataset_name}")
    print("=" * 60)
    
    dataset = braintrust.init_dataset(project=project_name, name=dataset_name)
    
    records_by_date = {}
    transaction_ids = set()
    
    for record in dataset:
        created_str = record.get('created', '')
        if created_str:
            created_dt = parse_iso_timestamp(created_str)
            date_key = created_dt.date()
            
            if date_key not in records_by_date:
                records_by_date[date_key] = []
            records_by_date[date_key].append(record)
        
        xact_id = record.get('_xact_id')
        if xact_id:
            transaction_ids.add(xact_id)
    
    print(f"Total records: {sum(len(records) for records in records_by_date.values())}")
    print(f"Unique transaction IDs: {len(transaction_ids)}")
    print(f"Records created across {len(records_by_date)} different dates")
    
    print("\nRecords by creation date:")
    for date_key in sorted(records_by_date.keys()):
        records = records_by_date[date_key]
        print(f"  {date_key}: {len(records)} records")
        
        # Show first few records for this date
        for i, record in enumerate(records[:3]):
            created_dt = parse_iso_timestamp(record['created'])
            print(f"    [{i+1}] {record['id']} - {format_timestamp(created_dt)}")
        
        if len(records) > 3:
            print(f"    ... and {len(records) - 3} more")


def main():
    """Main function to demonstrate dataset timestamp tracking."""
    project_name = "Dataset Timestamps Demo"
    dataset_name = "timestamp_tracking_example"
    
    try:
        # Demonstrate tracking changes
        track_dataset_changes(project_name, dataset_name)
        
        # Analyze the dataset we just created
        analyze_dataset_timestamps(project_name, dataset_name)
        
        print("\n" + "=" * 60)
        print("Key takeaways:")
        print("- Each dataset record has a 'created' field with ISO timestamp")
        print("- Updates don't change the 'created' timestamp")
        print("- Use '_xact_id' to detect when records have been updated")
        print("- Transaction IDs increase monotonically with each update")
        print("- You can add custom timestamp metadata for tracking updates")
        
    except Exception as e:
        print(f"Error: {e}")
        print("Make sure you have Braintrust configured with valid credentials.")


if __name__ == "__main__":
    main()
